# 数据源切换功能文档

## 功能概述

数据源切换功能用于控制真实网络数据和模拟数据的转发，确保同一时间只有一种数据源在转发数据到无人艇。

## 业务需求

### 问题背景
- 系统同时支持真实网络数据和模拟数据
- 需要确保真实数据和模拟数据不会同时转发
- 需要提供便捷的切换机制

### 解决方案
- 引入数据源模式概念（真实数据模式 vs 模拟数据模式）
- 修改 `shouldForwardData` 方法，加入数据源模式判断
- 提供完整的API接口进行模式切换

## 技术实现

### 1. 数据源模式枚举

```java
public enum DataSourceModeEnum {
    REAL_DATA("real", "真实数据"),           // 真实网络数据模式
    SIMULATION_DATA("simulation", "模拟数据"); // 模拟数据模式
}
```

### 2. 转发逻辑优化

#### 原有逻辑
```java
private boolean shouldForwardData(ExternalDataTypeEnum dataTypeEnum) {
    return dataTypeEnum.equals(ExternalDataTypeEnum.PLANE_DATA) || 
           externalDataConfigService.getFollowStatus();
}
```

#### 优化后逻辑
```java
private boolean shouldForwardData(ExternalDataTypeEnum dataTypeEnum) {
    // 基本转发条件
    boolean basicCondition = dataTypeEnum.equals(ExternalDataTypeEnum.PLANE_DATA) || 
                            externalDataConfigService.getFollowStatus();
    
    // 数据源模式条件：只有真实数据模式才转发网络接收的数据
    boolean dataSourceCondition = externalDataConfigService.isRealDataMode();
    
    return basicCondition && dataSourceCondition;
}
```

### 3. 模拟数据转发

新增 `sendSimulationData2Ship` 方法专门处理模拟数据转发：

```java
public void sendSimulationData2Ship(Point2D.Double point) {
    // 只有在模拟数据模式下才转发模拟数据
    if (!externalDataConfigService.isSimulationDataMode()) {
        return;
    }
    // 转发逻辑...
}
```

## API接口

### 基础路径
```
/external/config
```

### 数据源模式管理接口

#### 1. 获取当前数据源模式
```
GET /external/config/datasource/mode
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "mode": "real",
    "modeText": "真实数据",
    "isRealData": true,
    "isSimulation": false
  }
}
```

#### 2. 设置数据源模式
```
POST /external/config/datasource/mode?mode=real
POST /external/config/datasource/mode?mode=simulation
```

#### 3. 切换数据源模式
```
POST /external/config/datasource/toggle
```

#### 4. 快捷切换接口
```
POST /external/config/datasource/real        # 切换到真实数据模式
POST /external/config/datasource/simulation  # 切换到模拟数据模式
```

### 模拟数据发送接口

#### 发送模拟数据
```
POST /external/monitor/ship/simulation/send?longitude=116.123&latitude=39.456
```

## 工作模式

### 真实数据模式 (REAL_DATA)
- **转发真实网络数据**: ✅ 是
- **转发模拟数据**: ❌ 否
- **适用场景**: 生产环境，接收真实的网络数据

### 模拟数据模式 (SIMULATION_DATA)
- **转发真实网络数据**: ❌ 否
- **转发模拟数据**: ✅ 是
- **适用场景**: 测试环境，使用模拟数据进行测试

## 数据流控制

### 真实数据流
```
网络数据接收 -> shouldForwardData检查 -> [真实数据模式?] -> 转发到无人艇
                                      -> [模拟数据模式?] -> 丢弃数据
```

### 模拟数据流
```
模拟数据生成 -> sendSimulationData2Ship -> [模拟数据模式?] -> 转发到无人艇
                                        -> [真实数据模式?] -> 丢弃数据
```

## 使用示例

### 1. 切换到真实数据模式
```bash
# 切换到真实数据模式
curl -X POST "http://localhost:8080/external/config/datasource/real"

# 验证当前模式
curl -X GET "http://localhost:8080/external/config/datasource/mode"
```

### 2. 切换到模拟数据模式并发送测试数据
```bash
# 切换到模拟数据模式
curl -X POST "http://localhost:8080/external/config/datasource/simulation"

# 发送模拟数据
curl -X POST "http://localhost:8080/external/monitor/ship/simulation/send?longitude=116.123456&latitude=39.654321"
```

### 3. 前端JavaScript示例
```javascript
// 获取当前数据源模式
async function getCurrentMode() {
    const response = await fetch('/external/config/datasource/mode');
    const data = await response.json();
    console.log('当前模式:', data.data.modeText);
    return data.data;
}

// 切换到真实数据模式
async function switchToRealData() {
    const response = await fetch('/external/config/datasource/real', { method: 'POST' });
    const data = await response.json();
    console.log(data.data.message);
}

// 切换到模拟数据模式
async function switchToSimulation() {
    const response = await fetch('/external/config/datasource/simulation', { method: 'POST' });
    const data = await response.json();
    console.log(data.data.message);
}

// 发送模拟数据
async function sendSimulationData(longitude, latitude) {
    const response = await fetch(`/external/monitor/ship/simulation/send?longitude=${longitude}&latitude=${latitude}`, 
                                { method: 'POST' });
    const data = await response.json();
    console.log(data.message);
}
```

## 配置持久化

### Redis缓存键
- 跟随状态: `external:data:follow:status`
- 数据源模式: `external:data:source:mode`

### 默认配置
- 跟随状态: `false` (关闭)
- 数据源模式: `real` (真实数据模式)

## 日志示例

### 真实数据模式日志
```
INFO  - 数据源模式已设置为: 真实数据
INFO  - 转发无人机数据经纬度数据到无人艇， 经度：116.123456， 纬度：39.654321
DEBUG - 数据转发被跳过：当前为模拟数据模式，不转发真实网络数据，数据类型：无人机数据
```

### 模拟数据模式日志
```
INFO  - 数据源模式已设置为: 模拟数据
INFO  - 发送模拟数据到无人艇，经度：116.123456，纬度：39.654321
DEBUG - 当前为真实数据模式，不发送模拟数据
```

## 注意事项

### 1. 互斥性保证
- 真实数据和模拟数据在任何时候都不会同时转发
- 通过数据源模式严格控制数据流向

### 2. 状态一致性
- 配置状态会持久化到Redis
- 服务重启后自动恢复配置状态

### 3. 调试支持
- 提供详细的日志记录
- 支持缓存状态查询和清理

### 4. 性能考虑
- 使用内存缓存提高判断效率
- 5秒间隔控制依然有效，避免频繁发送

## 测试验证

### 单元测试覆盖
- 数据源模式枚举功能测试
- 配置服务状态管理测试
- 模式切换逻辑测试
- 互斥性验证测试

### 集成测试场景
- 真实数据模式下的网络数据转发
- 模拟数据模式下的模拟数据转发
- 模式切换时的数据流控制
- 配置持久化和恢复测试
