# sendData2Ship 方法优化文档

## 优化背景

### 原有问题
- `sendData2Ship` 方法被高频调用（约200ms一次）
- 每次调用都会立即转发数据，造成网络负载过高
- 没有数据发送频率控制机制

### 优化需求
- 将数据转发频率控制为5秒一次
- 保持数据的实时性（发送最新数据）
- 确保线程安全

## 优化方案

### 1. 核心设计思路

```
接收数据(200ms) -> 缓存最新数据 -> 5秒间隔检查 -> 发送最新数据
```

### 2. 技术实现

#### 数据缓存结构
```java
private static class CachedShipData {
    private final Map<String, Object> data;           // 缓存的数据
    private final AtomicLong lastSendTime;            // 上次发送时间戳
    private final ExternalDataTypeEnum dataType;      // 数据类型
}
```

#### 时间控制机制
- 使用 `AtomicLong` 记录上次发送时间
- 使用 `compareAndSet` 确保线程安全的时间更新
- 发送间隔设置为 5000ms（5秒）

#### 数据缓存机制
- 使用 `ConcurrentHashMap` 按数据类型分别缓存
- 每次接收到新数据时更新缓存
- 发送时使用缓存中的最新数据

### 3. 方法重构

#### 原方法
```java
private void sendData2Ship(Map<String, Object> datum, ExternalDataTypeEnum dataTypeEnum) {
    if (dataTypeEnum.equals(ExternalDataTypeEnum.PLANE_DATA) || externalDataConfigService.getFollowStatus()) {
        // 直接发送数据
        doSend(datum);
    }
}
```

#### 优化后方法
```java
private void sendData2Ship(Map<String, Object> datum, ExternalDataTypeEnum dataTypeEnum) {
    // 1. 检查是否需要转发
    if (!shouldForwardData(dataTypeEnum)) return;
    
    // 2. 更新缓存数据
    CachedShipData cachedData = cachedDataMap.computeIfAbsent(cacheKey, 
        k -> new CachedShipData(datum, dataTypeEnum));
    cachedData.updateData(datum);
    
    // 3. 检查发送间隔并发送
    if (cachedData.tryUpdateSendTime()) {
        doSendData2Ship(cachedData.getData(), dataTypeEnum);
    }
}
```

## 功能特性

### 1. 频率控制
- **发送间隔**: 5秒
- **数据实时性**: 始终发送最新数据
- **线程安全**: 使用原子操作和并发集合

### 2. 按类型分离
- 不同数据类型独立控制发送频率
- 缓存key格式: `ship_data_{dataType}`

### 3. 监控功能
- 提供缓存状态查询接口
- 支持缓存清理操作
- 详细的日志记录

## API接口

### 监控接口

#### 1. 获取缓存状态
```
GET /external/monitor/ship/cache/status
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "cacheSize": 2,
    "sendIntervalMs": 5000,
    "cacheDetails": {
      "ship_data_plane": {
        "dataType": "无人机数据",
        "lastSendTime": 1692345678901,
        "canSend": false,
        "timeSinceLastSend": 3000
      }
    }
  }
}
```

#### 2. 清空缓存
```
POST /external/monitor/ship/cache/clear
```

## 性能优化效果

### 优化前
- **发送频率**: 每200ms发送一次
- **每分钟发送次数**: 300次
- **网络负载**: 高

### 优化后
- **发送频率**: 每5秒发送一次
- **每分钟发送次数**: 12次
- **网络负载**: 降低96%
- **数据实时性**: 保持（发送最新数据）

## 使用示例

### 监控缓存状态
```bash
# 查看当前缓存状态
curl -X GET "http://localhost:8080/external/monitor/ship/cache/status"

# 清空缓存
curl -X POST "http://localhost:8080/external/monitor/ship/cache/clear"
```

### 日志示例
```
2024-01-15 10:30:15.123 INFO  - 转发无人机数据经纬度数据到无人艇， 经度：116.123456， 纬度：39.654321
2024-01-15 10:30:15.456 DEBUG - 数据转发被限流，距离上次发送不足5秒，数据类型：无人机数据
2024-01-15 10:30:20.789 INFO  - 成功转发无人机数据数据到无人艇，经度：116.123789，纬度：39.654789
```

## 配置参数

### 发送间隔配置
```java
private static final long SEND_INTERVAL_MS = 5000L; // 5秒间隔
```

可以根据实际需求调整此参数。

## 注意事项

### 1. 内存使用
- 缓存会占用少量内存存储最新数据
- 缓存大小与数据类型数量成正比
- 提供缓存清理功能防止内存泄漏

### 2. 线程安全
- 使用 `ConcurrentHashMap` 确保并发安全
- 使用原子操作更新时间戳
- 数据更新使用同步块保护

### 3. 数据一致性
- 始终发送最新接收到的数据
- 不会丢失数据，只是控制发送频率

## 测试验证

### 单元测试
- 测试发送间隔控制逻辑
- 测试缓存状态监控功能
- 测试高频数据接收场景

### 集成测试
- 验证与现有系统的兼容性
- 测试不同数据类型的独立控制
- 验证网络负载降低效果

## 后续优化建议

1. **可配置化**: 将发送间隔配置化，支持动态调整
2. **监控告警**: 添加缓存大小和发送频率的监控告警
3. **性能统计**: 记录发送成功率和响应时间统计
4. **批量发送**: 考虑支持批量发送多个数据点
