# 外部数据配置管理API文档

## 概述

本文档描述了外部数据配置管理相关的API接口，主要用于控制数据跟随功能的开启和关闭。

## 重构说明

### 原有问题
- `ExternalDataServiceImpl` 类中包含静态变量 `isOpen`，违反了单一职责原则
- 缺少对应的控制接口来管理开关状态
- 配置状态没有持久化，重启后会丢失

### 重构方案
1. 创建专门的配置管理服务 `ExternalDataConfigService`
2. 将 `isOpen` 参数重命名为更语义化的 `followStatus`
3. 使用Redis进行状态持久化
4. 提供完整的REST API接口

## API接口

### 基础路径
```
/external/config
```

### 1. 获取数据跟随状态

**接口地址：** `GET /external/config/follow/status`

**描述：** 获取当前数据跟随状态

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "followStatus": true,
    "statusText": "开启"
  }
}
```

### 2. 设置数据跟随状态

**接口地址：** `POST /external/config/follow/status`

**描述：** 设置数据跟随状态

**请求参数：**
- `status` (Boolean, 必填): 跟随状态，true表示开启，false表示关闭

**请求示例：**
```
POST /external/config/follow/status?status=true
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "followStatus": true,
    "statusText": "开启",
    "message": "数据跟随状态已设置为: 开启"
  }
}
```

### 3. 切换数据跟随状态

**接口地址：** `POST /external/config/follow/toggle`

**描述：** 切换当前数据跟随状态（开启变关闭，关闭变开启）

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "followStatus": false,
    "statusText": "关闭",
    "message": "数据跟随状态已切换为: 关闭"
  }
}
```

### 4. 开启数据跟随

**接口地址：** `POST /external/config/follow/enable`

**描述：** 直接开启数据跟随

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "followStatus": true,
    "statusText": "开启",
    "message": "数据跟随已开启"
  }
}
```

### 5. 关闭数据跟随

**接口地址：** `POST /external/config/follow/disable`

**描述：** 直接关闭数据跟随

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "followStatus": false,
    "statusText": "关闭",
    "message": "数据跟随已关闭"
  }
}
```

## 业务逻辑说明

### 数据跟随功能
当数据跟随功能开启时，系统会将无人机的经纬度数据转发给无人艇。具体逻辑在 `ExternalDataServiceImpl.sendData2Ship()` 方法中：

```java
if (dataTypeEnum.equals(ExternalDataTypeEnum.PLANE_DATA) || externalDataConfigService.getFollowStatus()) {
    // 转发数据到无人艇
}
```

### 状态持久化
- 配置状态会自动保存到Redis缓存中
- 服务重启后会自动从缓存中恢复状态
- 如果缓存中没有数据，默认状态为关闭

## 使用示例

### 前端JavaScript调用示例

```javascript
// 获取当前状态
fetch('/external/config/follow/status')
  .then(response => response.json())
  .then(data => {
    console.log('当前状态:', data.data.statusText);
  });

// 开启跟随
fetch('/external/config/follow/enable', { method: 'POST' })
  .then(response => response.json())
  .then(data => {
    console.log(data.data.message);
  });

// 关闭跟随
fetch('/external/config/follow/disable', { method: 'POST' })
  .then(response => response.json())
  .then(data => {
    console.log(data.data.message);
  });
```

### curl命令示例

```bash
# 获取状态
curl -X GET "http://localhost:8080/external/config/follow/status"

# 开启跟随
curl -X POST "http://localhost:8080/external/config/follow/enable"

# 关闭跟随
curl -X POST "http://localhost:8080/external/config/follow/disable"

# 切换状态
curl -X POST "http://localhost:8080/external/config/follow/toggle"
```
