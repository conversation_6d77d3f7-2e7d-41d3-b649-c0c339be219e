package com.gy.show.service;

import com.gy.show.entity.dto.ConfirmScheduleDTO;
import com.gy.show.enums.ExternalDataTypeEnum;
import com.gy.show.socket.message.HeadMessage;

import java.awt.geom.Point2D;
import java.util.List;
import java.util.Map;

public interface ExternalDataService {

    void handlerBusinessData(Object msg, String id, String ter);

    void handlerRequirementData(Object msg, ExternalDataTypeEnum dataTypeEnum, String id);

    HeadMessage parseControlMessageHead(Object msg);

    void handlerTargetData(Object msg, ExternalDataTypeEnum dataTypeEnum, String id);

    void handlerSituationData(Object msg, ExternalDataTypeEnum dataTypeEnum, String ter);

    void packageScheduleResult(List<ConfirmScheduleDTO> confirmScheduleDTOs);

    void handlerResponseScheduleResult(Object msg);

    void handlerControlData(Object msg, ExternalDataTypeEnum carData, String id, String ter);

    void sendBusinessData(byte[] data, String dataType);

    void simulatePushBzData();

    /**
     * 获取数据转发缓存状态
     */
    Map<String, Object> getShipDataCacheStatus();

    /**
     * 清空数据转发缓存
     */
    void clearShipDataCache();

    /**
     * 发送模拟数据到无人艇
     * @param point 经纬度坐标点
     */
    void sendSimulationData2Ship(Point2D.Double point);

    void send2Ship(Point2D.Double point);
}
