package com.gy.show.service;

import com.gy.show.enums.DataSourceModeEnum;
import com.gy.show.service.impl.ExternalDataConfigServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据源切换功能测试
 */
@ExtendWith(MockitoExtension.class)
public class DataSourceSwitchTest {

    private ExternalDataConfigService externalDataConfigService;

    @BeforeEach
    void setUp() {
        externalDataConfigService = new ExternalDataConfigServiceImpl();
    }

    @Test
    void testDefaultDataSourceMode() {
        // 测试默认数据源模式应该是真实数据
        assertEquals(DataSourceModeEnum.REAL_DATA, externalDataConfigService.getDataSourceMode());
        assertTrue(externalDataConfigService.isRealDataMode());
        assertFalse(externalDataConfigService.isSimulationDataMode());
    }

    @Test
    void testSetDataSourceMode_RealData() {
        // 测试设置为真实数据模式
        externalDataConfigService.setDataSourceMode(DataSourceModeEnum.REAL_DATA);
        
        assertEquals(DataSourceModeEnum.REAL_DATA, externalDataConfigService.getDataSourceMode());
        assertTrue(externalDataConfigService.isRealDataMode());
        assertFalse(externalDataConfigService.isSimulationDataMode());
    }

    @Test
    void testSetDataSourceMode_SimulationData() {
        // 测试设置为模拟数据模式
        externalDataConfigService.setDataSourceMode(DataSourceModeEnum.SIMULATION_DATA);
        
        assertEquals(DataSourceModeEnum.SIMULATION_DATA, externalDataConfigService.getDataSourceMode());
        assertFalse(externalDataConfigService.isRealDataMode());
        assertTrue(externalDataConfigService.isSimulationDataMode());
    }

    @Test
    void testSetDataSourceMode_Null() {
        // 测试设置null值应该抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            externalDataConfigService.setDataSourceMode(null);
        });
    }

    @Test
    void testToggleDataSourceMode() {
        // 测试切换数据源模式
        DataSourceModeEnum initialMode = externalDataConfigService.getDataSourceMode();
        
        // 第一次切换
        DataSourceModeEnum newMode1 = externalDataConfigService.toggleDataSourceMode();
        assertNotEquals(initialMode, newMode1);
        assertEquals(newMode1, externalDataConfigService.getDataSourceMode());
        
        // 第二次切换，应该回到初始模式
        DataSourceModeEnum newMode2 = externalDataConfigService.toggleDataSourceMode();
        assertEquals(initialMode, newMode2);
        assertEquals(initialMode, externalDataConfigService.getDataSourceMode());
    }

    @Test
    void testDataSourceModeEnum() {
        // 测试枚举的基本功能
        assertEquals("real", DataSourceModeEnum.REAL_DATA.getCode());
        assertEquals("真实数据", DataSourceModeEnum.REAL_DATA.getDescription());
        assertTrue(DataSourceModeEnum.REAL_DATA.isRealData());
        assertFalse(DataSourceModeEnum.REAL_DATA.isSimulationData());

        assertEquals("simulation", DataSourceModeEnum.SIMULATION_DATA.getCode());
        assertEquals("模拟数据", DataSourceModeEnum.SIMULATION_DATA.getDescription());
        assertFalse(DataSourceModeEnum.SIMULATION_DATA.isRealData());
        assertTrue(DataSourceModeEnum.SIMULATION_DATA.isSimulationData());
    }

    @Test
    void testDataSourceModeEnum_FromCode() {
        // 测试从代码获取枚举
        assertEquals(DataSourceModeEnum.REAL_DATA, DataSourceModeEnum.fromCode("real"));
        assertEquals(DataSourceModeEnum.SIMULATION_DATA, DataSourceModeEnum.fromCode("simulation"));
        
        // 测试无效代码
        assertThrows(IllegalArgumentException.class, () -> {
            DataSourceModeEnum.fromCode("invalid");
        });
    }

    @Test
    void testMutualExclusion() {
        // 测试真实数据和模拟数据模式的互斥性
        
        // 设置为真实数据模式
        externalDataConfigService.setDataSourceMode(DataSourceModeEnum.REAL_DATA);
        assertTrue(externalDataConfigService.isRealDataMode());
        assertFalse(externalDataConfigService.isSimulationDataMode());
        
        // 切换到模拟数据模式
        externalDataConfigService.setDataSourceMode(DataSourceModeEnum.SIMULATION_DATA);
        assertFalse(externalDataConfigService.isRealDataMode());
        assertTrue(externalDataConfigService.isSimulationDataMode());
    }

    @Test
    void testCombinedConfiguration() {
        // 测试数据源模式与跟随状态的组合配置
        
        // 开启跟随状态
        externalDataConfigService.setFollowStatus(true);
        assertTrue(externalDataConfigService.getFollowStatus());
        
        // 设置为真实数据模式
        externalDataConfigService.setDataSourceMode(DataSourceModeEnum.REAL_DATA);
        assertTrue(externalDataConfigService.isRealDataMode());
        
        // 设置为模拟数据模式
        externalDataConfigService.setDataSourceMode(DataSourceModeEnum.SIMULATION_DATA);
        assertTrue(externalDataConfigService.isSimulationDataMode());
        
        // 跟随状态应该保持不变
        assertTrue(externalDataConfigService.getFollowStatus());
    }
}
