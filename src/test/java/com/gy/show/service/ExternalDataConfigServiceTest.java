package com.gy.show.service;

import com.gy.show.service.impl.ExternalDataConfigServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 外部数据配置服务测试
 */
@ExtendWith(MockitoExtension.class)
public class ExternalDataConfigServiceTest {

    private ExternalDataConfigService externalDataConfigService;

    @BeforeEach
    void setUp() {
        externalDataConfigService = new ExternalDataConfigServiceImpl();
    }

    @Test
    void testGetFollowStatus_DefaultValue() {
        // 测试默认值应该是false
        assertFalse(externalDataConfigService.getFollowStatus());
    }

    @Test
    void testSetFollowStatus_True() {
        // 测试设置为true
        externalDataConfigService.setFollowStatus(true);
        assertTrue(externalDataConfigService.getFollowStatus());
    }

    @Test
    void testSetFollowStatus_False() {
        // 测试设置为false
        externalDataConfigService.setFollowStatus(false);
        assertFalse(externalDataConfigService.getFollowStatus());
    }

    @Test
    void testToggleFollowStatus() {
        // 测试切换功能
        boolean initialStatus = externalDataConfigService.getFollowStatus();
        boolean newStatus = externalDataConfigService.toggleFollowStatus();
        
        // 新状态应该与初始状态相反
        assertEquals(!initialStatus, newStatus);
        assertEquals(newStatus, externalDataConfigService.getFollowStatus());
    }

    @Test
    void testToggleFollowStatus_Multiple() {
        // 测试多次切换
        boolean status1 = externalDataConfigService.toggleFollowStatus();
        boolean status2 = externalDataConfigService.toggleFollowStatus();
        boolean status3 = externalDataConfigService.toggleFollowStatus();
        
        // 奇数次切换后应该与初始状态相反，偶数次切换后应该与初始状态相同
        assertNotEquals(status1, status2);
        assertEquals(status1, status3);
    }
}
