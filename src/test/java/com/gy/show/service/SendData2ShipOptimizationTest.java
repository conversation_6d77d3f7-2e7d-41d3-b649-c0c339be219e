package com.gy.show.service;

import com.gy.show.enums.ExternalDataTypeEnum;
import com.gy.show.service.impl.ExternalDataServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * sendData2Ship方法优化测试
 */
@ExtendWith(MockitoExtension.class)
public class SendData2ShipOptimizationTest {

    @Mock
    private ExternalDataConfigService externalDataConfigService;

    private ExternalDataServiceImpl externalDataService;

    @BeforeEach
    void setUp() {
        externalDataService = new ExternalDataServiceImpl();
        ReflectionTestUtils.setField(externalDataService, "externalDataConfigService", externalDataConfigService);
        
        // 模拟跟随状态为开启
        when(externalDataConfigService.getFollowStatus()).thenReturn(true);
    }

    @Test
    void testSendIntervalControl() throws InterruptedException {
        // 准备测试数据
        Map<String, Object> testData = new HashMap<>();
        testData.put("longitude", "116.123456");
        testData.put("latitude", "39.654321");

        // 模拟快速调用sendData2Ship方法
        AtomicInteger actualSendCount = new AtomicInteger(0);
        
        // 由于我们无法直接测试私有方法，这里测试缓存状态
        Map<String, Object> initialStatus = externalDataService.getShipDataCacheStatus();
        assertEquals(0, initialStatus.get("cacheSize"));

        // 清空缓存确保测试环境干净
        externalDataService.clearShipDataCache();
        
        Map<String, Object> clearedStatus = externalDataService.getShipDataCacheStatus();
        assertEquals(0, clearedStatus.get("cacheSize"));
        assertEquals(5000L, clearedStatus.get("sendIntervalMs"));
    }

    @Test
    void testCacheStatusMonitoring() {
        // 测试缓存状态监控功能
        Map<String, Object> status = externalDataService.getShipDataCacheStatus();
        
        assertNotNull(status);
        assertTrue(status.containsKey("cacheSize"));
        assertTrue(status.containsKey("sendIntervalMs"));
        assertTrue(status.containsKey("cacheDetails"));
        
        assertEquals(5000L, status.get("sendIntervalMs"));
    }

    @Test
    void testCacheClear() {
        // 测试缓存清理功能
        externalDataService.clearShipDataCache();
        
        Map<String, Object> status = externalDataService.getShipDataCacheStatus();
        assertEquals(0, status.get("cacheSize"));
    }

    @Test
    void testMultipleDataTypes() {
        // 测试不同数据类型的缓存是否独立
        Map<String, Object> status = externalDataService.getShipDataCacheStatus();
        
        // 验证缓存详情结构
        Map<String, Object> cacheDetails = (Map<String, Object>) status.get("cacheDetails");
        assertNotNull(cacheDetails);
    }

    /**
     * 模拟高频数据接收场景的集成测试
     */
    @Test
    void testHighFrequencyDataScenario() throws InterruptedException {
        // 这个测试模拟200ms接收一次数据，验证5秒间隔发送的逻辑
        
        Map<String, Object> testData = new HashMap<>();
        testData.put("longitude", "116.123456");
        testData.put("latitude", "39.654321");

        // 记录初始状态
        Map<String, Object> initialStatus = externalDataService.getShipDataCacheStatus();
        int initialCacheSize = (Integer) initialStatus.get("cacheSize");

        // 模拟快速数据更新（每200ms一次，持续2秒）
        CountDownLatch latch = new CountDownLatch(1);
        
        Thread dataSimulator = new Thread(() -> {
            try {
                for (int i = 0; i < 10; i++) {
                    // 更新数据
                    testData.put("longitude", "116." + (123456 + i));
                    testData.put("latitude", "39." + (654321 + i));
                    
                    // 这里我们无法直接调用私有方法，但可以验证缓存状态
                    Thread.sleep(200); // 模拟200ms间隔
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        });

        dataSimulator.start();
        
        // 等待数据模拟完成
        assertTrue(latch.await(5, TimeUnit.SECONDS));
        
        // 验证缓存状态
        Map<String, Object> finalStatus = externalDataService.getShipDataCacheStatus();
        assertNotNull(finalStatus);
        
        // 验证发送间隔配置正确
        assertEquals(5000L, finalStatus.get("sendIntervalMs"));
    }
}
